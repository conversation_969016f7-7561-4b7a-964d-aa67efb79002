# -*- coding: UTF-8 -*-
"""
__Author__ = "MakiNaruto"
__Version__ = "2.1.0"
__Description__ = ""
__Created__ = 2022/2/14 10:37 下午
"""

import re
import os
import json
import tools
import argparse
import requests
import time
import random
from requests import session


class DaMaiTicket:
    def __init__(self):
        # 登录信息
        self.login_cookies = {}
        self.session = session()
        self.login_id: str = '13798489587'  # 大麦网登录账户名
        self.login_password: str = 'AI19930801520'  # 大麦网登录密码
        # 以下为抢票必须的参数
        self.item_id: int = 968367050836  # 商品id
        self.viewer: list = ['张佳伟']  # 在大麦网已填写的观影人
        self.buy_nums: int = 1  # 购买影票数量, 需与观影人数量一致
        self.ticket_price: int = 1399  # 购买指定票价

    def step1_get_order_info(self, item_id, commodity_param, ticket_price=None):
        """
        获取点击购买所必须的参数信息
        :param item_id:             商品id
        :param commodity_param:     获取商品购买信息必须的参数
        :param ticket_price:        购买指定价位的票
        :return:
        """
        if not ticket_price:
            print('-' * 10, '票价未填写, 请选择票价', '-' * 10)
            return False

        commodity_param.update({'itemId': item_id})

        # 添加随机延时，模拟人类行为
        time.sleep(random.uniform(1, 3))

        # 尝试获取真正的票务信息API
        # 基于分析，当前的subpage API返回的是配置信息，不是票务信息
        # 需要找到正确的票务API端点

        print("尝试获取票务信息...")

        # 首先尝试访问商品页面获取完整信息
        page_url = f'https://detail.damai.cn/item.htm?id={item_id}'
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }

        try:
            print(f"访问商品页面: {page_url}")
            response = self.session.get(page_url, headers=headers)

            if response.status_code != 200:
                print(f"访问商品页面失败，状态码: {response.status_code}")
                return False, None, None

            # 从页面中提取票务信息
            ticket_info = self._extract_ticket_info_from_page(response.text)
            if ticket_info:
                return ticket_info

            # 如果页面解析失败，尝试原始API（用于调试）
            print("页面解析失败，尝试原始API...")
            headers['referer'] = page_url
            response = self.session.get('https://detail.damai.cn/subpage', headers=headers, params=commodity_param)

            print(f"原始API响应状态码: {response.status_code}")
            print(f"响应内容前200字符: {response.text[:500]}")

            # 保存调试信息
            with open('debug_current_response.txt', 'w', encoding='utf-8') as f:
                f.write(f"商品ID: {item_id}\n")
                f.write(f"API URL: https://detail.damai.cn/subpage\n")
                f.write(f"参数: {commodity_param}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"响应内容:\n{response.text}")

            print("调试信息已保存到 debug_current_response.txt")

            # 尝试解析这个响应（即使它可能不包含票务信息）
            ticket_info = self._parse_response(response.text, 0)
            if ticket_info:
                # 保存解析后的JSON
                with open('debug_parsed_response.json', 'w', encoding='utf-8') as f:
                    json.dump(ticket_info, f, ensure_ascii=False, indent=2)
                print("解析后的响应已保存到 debug_parsed_response.json")

            print("当前API可能已经改变，建议检查大麦网的最新API接口")
            return False, None, None

        except Exception as e:
            print(f"获取票务信息时发生错误: {e}")
            return False, None, None

    def _parse_response(self, response_text, endpoint_idx):
        """解析不同API端点的响应"""
        try:
            response_text = response_text.strip()

            # 检查是否为空响应
            if not response_text:
                print("错误: 响应内容为空")
                return None

            # 检查是否遇到反爬虫机制
            if '__baxia__' in response_text or 'punishPath' in response_text:
                print("错误: 遇到反爬虫验证，请求被拦截")
                return None

            # 检查是否为JavaScript函数而非JSON
            if response_text.startswith('(function') or response_text.startswith('!function'):
                print("错误: 响应为JavaScript代码，非预期的JSON数据")
                return None

            # 处理不同的响应格式
            if endpoint_idx == 0:  # 原始subpage API
                # 处理JSONP格式
                if response_text.startswith('null(') and response_text.endswith(')'):
                    json_str = response_text[5:-1]
                elif response_text.startswith('__jp0(') and response_text.endswith(')'):
                    json_str = response_text[6:-1]
                elif response_text.startswith('callback(') and response_text.endswith(')'):
                    json_str = response_text[9:-1]
                else:
                    json_str = response_text
            elif endpoint_idx == 1:  # MTOP API
                # 处理MTOP JSONP格式
                if response_text.startswith('mtopjsonp1(') and response_text.endswith(')'):
                    json_str = response_text[11:-1]
                else:
                    json_str = response_text
            else:  # 商品页面
                # 从HTML中提取JSON数据
                import re
                json_match = re.search(r'window\.__INIT_DATA__\s*=\s*({.*?});', response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group(1)
                else:
                    print("无法从HTML页面中提取JSON数据")
                    return None

            # 尝试解析JSON
            ticket_info = json.loads(json_str)

            # 保存调试信息
            debug_filename = f'debug_response_endpoint_{endpoint_idx}.json'
            with open(debug_filename, 'w', encoding='utf-8') as f:
                json.dump(ticket_info, f, ensure_ascii=False, indent=2)
            print(f"响应已保存到 {debug_filename}")

            return ticket_info

        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return None
        except Exception as e:
            print(f"处理响应时发生错误: {e}")
            return None

    def _extract_sku_info(self, ticket_info, ticket_price):
        """从不同的响应结构中提取SKU信息"""
        try:
            # 基于实际响应结构，当前响应缺少perform字段
            # 需要采用不同的策略获取票务信息

            print("当前响应结构分析:")
            print(f"可用字段: {list(ticket_info.keys()) if isinstance(ticket_info, dict) else 'N/A'}")

            # 检查响应是否成功
            if 'responseInfo' in ticket_info:
                response_info = ticket_info['responseInfo']
                if response_info.get('responseSuccess') != 'true':
                    print(f"API响应失败: {response_info}")
                    return None

            # 当前响应结构表明这可能不是票务信息API
            # 而是其他类型的API响应，需要尝试不同的API端点

            # 检查是否有任何票务相关信息
            json_str = json.dumps(ticket_info, ensure_ascii=False)
            ticket_keywords = ['sku', 'price', 'ticket', 'buy', 'perform', 'seat']
            found_keywords = [kw for kw in ticket_keywords if kw in json_str.lower()]

            if found_keywords:
                print(f"在响应中找到票务相关关键词: {found_keywords}")
            else:
                print("响应中未找到票务相关信息，可能需要使用不同的API端点")

            # 尝试从现有结构中提取任何可用信息
            # 由于当前响应主要包含配置和日历信息，我们需要返回一个指示
            # 表明需要尝试其他API端点

            return None

        except Exception as e:
            print(f"提取SKU信息时发生错误: {e}")
            return None

    def _extract_ticket_info_from_page(self, html_content):
        """从商品页面HTML中提取票务信息"""
        try:
            import re

            # 查找页面中的JSON数据
            patterns = [
                r'window\.__INIT_DATA__\s*=\s*({.*?});',
                r'window\.dmPageData\s*=\s*({.*?});',
                r'window\.itemData\s*=\s*({.*?});',
                r'var\s+pageData\s*=\s*({.*?});'
            ]

            for pattern in patterns:
                match = re.search(pattern, html_content, re.DOTALL)
                if match:
                    try:
                        json_str = match.group(1)
                        page_data = json.loads(json_str)

                        print(f"从页面提取到JSON数据，模式: {pattern}")

                        # 保存提取的数据
                        with open('debug_page_data.json', 'w', encoding='utf-8') as f:
                            json.dump(page_data, f, ensure_ascii=False, indent=2)
                        print("页面数据已保存到 debug_page_data.json")

                        # 尝试从页面数据中提取票务信息
                        return self._extract_sku_from_page_data(page_data)

                    except json.JSONDecodeError as e:
                        print(f"JSON解析失败: {e}")
                        continue

            print("未在页面中找到有效的JSON数据")
            return None

        except Exception as e:
            print(f"从页面提取票务信息时发生错误: {e}")
            return None

    def _extract_sku_from_page_data(self, page_data):
        """从页面数据中提取SKU信息"""
        try:
            # 搜索可能包含票务信息的路径
            possible_paths = [
                ['detail', 'item', 'skuList'],
                ['detail', 'perform', 'skuList'],
                ['item', 'skuList'],
                ['perform', 'skuList'],
                ['data', 'item', 'skuList'],
                ['data', 'perform', 'skuList'],
                ['itemDetail', 'skuList'],
                ['performDetail', 'skuList']
            ]

            for path in possible_paths:
                try:
                    current = page_data
                    for key in path:
                        current = current[key]

                    if isinstance(current, list) and current:
                        print(f"找到SKU列表，路径: {' -> '.join(path)}")

                        # 查找匹配票价的SKU
                        for index, sku in enumerate(current):
                            if sku.get('price') and float(sku.get('price')) == float(self.ticket_price):
                                sku_id = sku.get('skuId', '')
                                return page_data, index, sku_id

                        # 如果没有找到匹配的票价，返回第一个SKU
                        if current:
                            print(f"未找到匹配票价 {self.ticket_price} 的SKU，使用第一个SKU")
                            first_sku = current[0]
                            sku_id = first_sku.get('skuId', '')
                            return page_data, 0, sku_id

                except (KeyError, TypeError, ValueError):
                    continue

            print("未在页面数据中找到SKU信息")
            print(f"页面数据顶级键: {list(page_data.keys()) if isinstance(page_data, dict) else 'N/A'}")
            return None

        except Exception as e:
            print(f"从页面数据提取SKU信息时发生错误: {e}")
            return None

    def _get_ticket_status(self, ticket_info, sku_id_sequence):
        """从不同的响应结构中获取票务状态"""
        try:
            # 尝试多种可能的状态获取路径
            status_paths = [
                # 原始结构
                ['skuPagePcBuyBtn', 'skuBtnList', sku_id_sequence, 'btnText'],
                # 可能的新结构
                ['data', 'skuPagePcBuyBtn', 'skuBtnList', sku_id_sequence, 'btnText'],
                ['perform', 'skuList', sku_id_sequence, 'status'],
                ['data', 'perform', 'skuList', sku_id_sequence, 'status'],
                # 从SKU信息中获取状态
                ['perform', 'skuList', sku_id_sequence, 'saleStatus'],
                ['data', 'perform', 'skuList', sku_id_sequence, 'saleStatus'],
            ]

            for path in status_paths:
                try:
                    current = ticket_info
                    for key in path:
                        if isinstance(key, int):
                            # 处理数组索引
                            if isinstance(current, list) and len(current) > key:
                                current = current[key]
                            else:
                                break
                        else:
                            # 处理对象键
                            current = current[key]

                    if isinstance(current, str) and current:
                        print(f"找到票务状态，路径: {' -> '.join(str(k) for k in path)}")
                        return current

                except (KeyError, TypeError, IndexError):
                    continue

            # 如果无法从标准路径获取状态，尝试分析整个响应结构
            print("无法从标准路径获取票务状态，尝试分析响应结构...")

            # 检查是否有可用的按钮信息
            if 'skuPagePcBuyBtn' in ticket_info:
                btn_info = ticket_info['skuPagePcBuyBtn']
                if 'skuBtnList' in btn_info and isinstance(btn_info['skuBtnList'], list):
                    if len(btn_info['skuBtnList']) > sku_id_sequence:
                        btn_data = btn_info['skuBtnList'][sku_id_sequence]
                        # 尝试多个可能的状态字段
                        for status_field in ['btnText', 'text', 'status', 'label']:
                            if status_field in btn_data:
                                return btn_data[status_field]

            # 如果仍然无法获取状态，返回默认状态
            print("无法获取票务状态，可能需要更新解析逻辑")
            print(f"可用的顶级字段: {list(ticket_info.keys()) if isinstance(ticket_info, dict) else 'N/A'}")

            # 返回None表示无法获取状态
            return None

        except Exception as e:
            print(f"获取票务状态时发生错误: {e}")
            return None

    def step2_click_buy_now(self, ex_params, sku_info):
        """
        点击立即购买
        :param ex_params:   点击立即购买按钮所发送请求的必须参数
        :param sku_info:    购买指定商品信息及数量信息
        :return:
        """

        headers = {
            'authority': 'buy.damai.cn',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="99", "Google Chrome";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'sec-fetch-site': 'same-site',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-user': '?1',
            'sec-fetch-dest': 'document',
            'referer': 'https://detail.damai.cn/',
            'accept-language': 'zh,en;q=0.9,en-US;q=0.8,zh-CN;q=0.7'
        }
        params = {
            'exParams': json.dumps(ex_params),
            'buyParam': sku_info,
            'buyNow': 'true',
            'spm': 'a2oeg.project.projectinfo.dbuy'
        }

        response = self.session.get('https://buy.damai.cn/orderConfirm', headers=headers,
                                    params=params, cookies=self.login_cookies)
        result = re.search('window.__INIT_DATA__[\s\S]*?};', response.text)
        self.login_cookies.update(self.session.cookies)
        try:
            submit_order_info = json.loads(result.group().replace('window.__INIT_DATA__ = ', '')[:-1])
            submit_order_info.update({'output': json.loads(submit_order_info.get('output'))})
        except Exception as e:
            print('-' * 10, '获取购买必备参数异常，请重新解析response返回的参数', '-' * 10)
            print(result.group())
            return False
        return submit_order_info

    def step2_click_confirm_select_seats(self, project_id, perform_id, seat_info, sku_info):
        """ 选座购买，点击确认选座 """
        headers = {
            'authority': 'buy.damai.cn',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'accept-language': 'zh,en;q=0.9,en-US;q=0.8,zh-CN;q=0.7',
            'cache-control': 'max-age=0',
            'referer': 'https://seatsvc.damai.cn/',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="100", "Google Chrome";v="100"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-site',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
        }

        params = {
            'exParams': json.dumps({'damai': '1',
                                    'channel': 'damai_app',
                                    'umpChannel': '10002',
                                    'atomSplit': '1',
                                    'seatInfo': seat_info,
                                    'serviceVersion': '2.0.0'}).replace(' ', ''),
            'buyParam': sku_info,
            'buyNow': 'true',
            'projectId': project_id,
            'performId': perform_id,
            'spm': 'a2oeg.selectseat.bottom.dbuy',
        }

        response = requests.get('https://buy.damai.cn/orderConfirm', params=params, cookies=self.login_cookies,
                                headers=headers)
        if response.status_code == 200:
            result = re.search('window.__INIT_DATA__[\s\S]*?};', response.text)
            self.login_cookies.update(self.session.cookies)
            try:
                submit_order_info = json.loads(result.group().replace('window.__INIT_DATA__ = ', '')[:-1])
                submit_order_info.update({'output': json.loads(submit_order_info.get('output'))})
            except Exception as e:
                print('-' * 10, '获取购买必备参数异常，请重新解析response返回的参数', '-' * 10)
                print(result.group())
                return False
            return submit_order_info

    def step3_submit_order(self, submit_order_info, viewer, seat_info=None):
        """
        提交订单所需参数信息
        :param submit_order_info:   最终确认订单所需的所有信息。
        :param viewer:  指定观演人进行购票
        :param seat_info:  座位id
        :return:
        """
        headers = {
            'authority': 'buy.damai.cn',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="98", "Google Chrome";v="98"',
            'accept': 'application/json, text/plain, */*',
            'content-type': 'application/json;charset=UTF-8',
            'x-requested-with': 'XMLHttpRequest',
            'sec-ch-ua-mobile': '?0',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36',
            'sec-ch-ua-platform': '"macOS"',
            'origin': 'https://buy.damai.cn',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'referer': 'https://buy.damai.cn/orderConfirm?',
            'accept-language': 'zh,en;q=0.9,en-US;q=0.8,zh-CN;q=0.7',
        }

        params = (
            ('feature', '{"returnUrl":"https://orders.damai.cn/orderDetail","serviceVersion":"1.8.5"}'),
            ('submitref', 'undefined'),
        )
        dm_viewer_pc = str([k for k, v in submit_order_info.get('data').items()])
        dm_viewer_pc_id_search = re.search('dmViewerPC_[0-9]*', dm_viewer_pc)
        if dm_viewer_pc_id_search:
            dm_viewer_pc_id = dm_viewer_pc_id_search.group()  # 获取到观演人的 key
            user_list = submit_order_info['data'][dm_viewer_pc_id]['fields']['dmViewerList']
            all_available_user = [name.get('viewerName') for name in user_list]
            if len(set(viewer).intersection(set(all_available_user))) != len(viewer):
                print('-' * 10, '请检查输入的观演人信息与大麦网观演人信息是否一致', '-' * 10)
                return False
            for user in user_list:
                if user.get('viewerName') in viewer:
                    user['isUsed'] = True
            # 若为选座购买, 则需要添加座位id。
            if seat_info:
                seat_info = [seat.get('seatId') for seat in seat_info]
                seat_index = 0
                for user in user_list:
                    if seat_index > len(viewer) - 1:
                        break
                    if user.get('viewerName') in viewer:
                        user['seatId'] = seat_info[seat_index]
                        seat_index += 1
        else:
            print("该场次不需要指定观演人")

        submit_order_info = json.dumps(submit_order_info)
        response = self.session.post('https://buy.damai.cn/multi/trans/createOrder',
                                     headers=headers,
                                     params=params,
                                     data=submit_order_info,
                                     cookies=self.login_cookies)
        buy_status = json.loads(response.text)
        if buy_status.get('success') is True and buy_status.get('module').get('alipayOrderId'):
            print('-' * 10, '抢票成功, 请前往 大麦网->我的大麦->交易中心->订单管理 确认订单', '-' * 10)
            print('alipayOrderId: ', buy_status.get('module').get('alipayOrderId'))
            print('支付宝支付链接: ', buy_status.get('module').get('alipayWapCashierUrl'))

    def run(self):
        if len(self.viewer) != self.buy_nums:
            print('-' * 10, '购买数量与实际观演人数量不符', '-' * 10)
            return
        if os.path.exists('cookies.pkl'):
            cookies = tools.load_cookies()
            self.login_cookies.update(cookies)
        elif 'account' == args.mode.lower():
            self.login_cookies = tools.account_login('account', self.login_id, self.login_password)
        else:
            self.login_cookies = tools.account_login('qr')

        login_status = tools.check_login_status(self.login_cookies)

        if not login_status:
            print('-' * 10, '登录失败, 请检查登录账号信息。若使用保存的cookies，则删除cookies文件重新尝试', '-' * 10)
            return
        elif login_status and not os.path.exists('cookies.pkl'):
            tools.save_cookies(self.login_cookies)

        commodity_param, ex_params = tools.get_api_param()

        submit_order_info = ''
        buy_serial_number = ''
        seat_info = None
        
        # 主抢票循环
        while True:
            # 获取订单信息，带重试机制
            retry_count = 0
            max_retries = 10
            ticket_info = None
            
            while retry_count < max_retries:
                result = self.step1_get_order_info(self.item_id, commodity_param, ticket_price=self.ticket_price)
                if not result or result[0] is False:
                    retry_count += 1
                    print(f'-' * 10, f'获取订单信息失败，第{retry_count}次重试...', '-' * 10)
                    
                    # 如果遇到反爬虫，增加更长的等待时间
                    if retry_count > 3:
                        wait_time = random.uniform(10, 20)
                        print(f"等待 {wait_time:.1f} 秒后重试...")
                        time.sleep(wait_time)
                    else:
                        time.sleep(random.uniform(2, 5))
                    continue
                
                ticket_info, sku_id_sequence, sku_id = result
                break
            
            if retry_count >= max_retries:
                print('-' * 10, '多次重试失败，可能遇到反爬虫限制，请稍后再试', '-' * 10)
                return
            
            # 处理票务状态 - 适配新的响应结构
            ticket_sku_status = self._get_ticket_status(ticket_info, sku_id_sequence)

            if not ticket_sku_status:
                print('-' * 10, '无法获取票务状态，继续重试...', '-' * 10)
                time.sleep(random.uniform(2, 5))
                continue

            print(f"当前票务状态: {ticket_sku_status}")

            if ticket_sku_status in ['即将开抢', '即将开售', '敬请期待']:
                print('-' * 10, '票还未开售，继续等待...', '-' * 10)
                time.sleep(random.uniform(1, 3))
                continue
            elif ticket_sku_status in ['缺货登记', '售罄', '已售完', '暂时缺货']:
                print('-' * 10, '手慢了，该票价已经售空: ', ticket_sku_status, '-' * 10)
                return False
            elif ticket_sku_status in ['立即购买', '现货']:
                buy_serial_number = '{}_{}_{}'.format(self.item_id, self.buy_nums, sku_id)
                submit_order_info = self.step2_click_buy_now(ex_params, buy_serial_number)
                break
            elif ticket_sku_status in ['选座购买', '选座']:
                # 获取选座购买必备的数据信息。
                city_id, project_id, item_id, perform_id = tools.get_select_seat_params(self.item_id)
                stand_id, seat_price_list = tools.get_seat_dynamic_info(self.login_cookies, project_id, item_id,
                                                                        perform_id)
                api_address = tools.get_select_seat_api(self.login_cookies, perform_id, city_id)
                buy_serial_number = '{}_{}_{}'.format(self.item_id, self.buy_nums, sku_id)
                api_address += str(stand_id) + '.json'
                response = requests.get(api_address)
                if response.status_code != 200:
                    return
                # 获取全部的座位信息
                all_seats_info = json.loads(response.text)
                # 获取可售的座位信息
                valuable_info = tools.get_valuable_seat_id(self.login_cookies, project_id, perform_id, city_id,
                                                           stand_id)
                # 获取 指定抢票价格的 sku_id, price_id
                sku_id, price_id = None, None
                for sku_info in seat_price_list:
                    if self.ticket_price == int(sku_info.get('salePrice')):
                        sku_id = sku_info.get('skuId')
                        price_id = sku_info.get('priceId')
                        break
                if not sku_id or not price_id:
                    print('-' * 10, '获取sku_id失败', '-' * 10)
                    return

                """
                过滤无效座位信息，仅留下符合条件的座位id
                1. 仅保留目标价位下的座位id(暂时只支持一种目标价位)
                2. 过滤掉不可售的座位id。
                """
                valuable_seat = tools.format_valuable_seatid(all_seats_info, valuable_info, price_id)
                # 挑选座位
                seat_info = tools.pick_seat(valuable_seat, stand_id, self.buy_nums)
                submit_order_info = self.step2_click_confirm_select_seats(project_id, perform_id, seat_info,
                                                                          buy_serial_number)
                break
            else:
                print(f'-' * 10, f'未知的票务状态: {ticket_sku_status}，继续等待...', '-' * 10)
                time.sleep(random.uniform(2, 5))
                continue
        if not buy_serial_number or not submit_order_info:
            print('-' * 10, '获取购票所需信息失败', '-' * 10)
            return
        self.step3_submit_order(submit_order_info, self.viewer, seat_info)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='manual to this script')
    parser.add_argument('--mode', type=str, default='account', required=False,
                        help='account: account login， QR: Scan QR code login')
    args = parser.parse_args()
    a = DaMaiTicket()
    a.run()