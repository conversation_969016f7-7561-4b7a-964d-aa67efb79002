#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
调试脚本：用于测试大麦网API响应格式
"""

import requests
import json
from requests import session

def test_damai_api():
    """测试大麦网API响应格式"""
    
    # 使用你的商品ID
    item_id = 968367050836
    
    # 基本参数
    commodity_param = {
        'itemId': item_id,
        # 可能需要的其他参数
    }
    
    headers = {
        'authority': 'detail.damai.cn',
        'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="98", "Google Chrome";v="98"',
        'sec-ch-ua-mobile': '?0',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36',
        'sec-ch-ua-platform': '"macOS"',
        'accept': '*/*',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'no-cors',
        'sec-fetch-dest': 'script',
        'referer': 'https://detail.damai.cn/item.htm',
        'accept-language': 'zh,en;q=0.9,en-US;q=0.8,zh-CN;q=0.7',
    }
    
    session_obj = session()
    
    try:
        response = session_obj.get('https://detail.damai.cn/subpage', headers=headers, params=commodity_param)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应长度: {len(response.text)}")
        print(f"响应内容前1000字符:")
        print("-" * 50)
        print(response.text[:1000])
        print("-" * 50)
        
        # 尝试不同的解析方法
        response_text = response.text.strip()
        
        print("\n尝试解析JSON:")
        
        # 方法1: 原始方法
        try:
            json_str1 = response_text.replace('null(', '').replace('__jp0(', '')[:-1]
            result1 = json.loads(json_str1)
            print("✓ 原始方法成功")
        except Exception as e:
            print(f"✗ 原始方法失败: {e}")
        
        # 方法2: 检测JSONP格式
        try:
            if response_text.startswith('null('):
                json_str2 = response_text[5:-1]  # 去掉 'null(' 和 ')'
            elif response_text.startswith('__jp0('):
                json_str2 = response_text[6:-1]  # 去掉 '__jp0(' 和 ')'
            else:
                json_str2 = response_text
            
            result2 = json.loads(json_str2)
            print("✓ 改进方法成功")
            
            # 显示JSON结构
            print("\nJSON结构预览:")
            if isinstance(result2, dict):
                for key in list(result2.keys())[:5]:  # 只显示前5个键
                    print(f"  {key}: {type(result2[key])}")
            
        except Exception as e:
            print(f"✗ 改进方法失败: {e}")
            
        # 保存原始响应到文件
        with open('debug_response.txt', 'w', encoding='utf-8') as f:
            f.write(response.text)
        print("\n原始响应已保存到 debug_response.txt")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_damai_api()