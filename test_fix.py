#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
"""
测试修复后的代码
"""

from Automatic_ticket_purchase import DaMaiTicket
import tools

def test_ticket_purchase():
    """测试抢票功能"""
    
    # 创建实例
    ticket = DaMaiTicket()
    
    # 模拟登录状态（你需要先登录获取cookies）
    if os.path.exists('cookies.pkl'):
        cookies = tools.load_cookies()
        ticket.login_cookies.update(cookies)
    else:
        print("请先运行主程序进行登录")
        return
    
    # 获取API参数
    try:
        commodity_param, ex_params = tools.get_api_param()
    except Exception as e:
        print(f"获取API参数失败: {e}")
        return
    
    # 测试获取订单信息
    print("测试获取订单信息...")
    result = ticket.step1_get_order_info(ticket.item_id, commodity_param, ticket_price=ticket.ticket_price)
    
    if result and result[0] is not False:
        print("✓ 获取订单信息成功")
        ticket_info, sku_id_sequence, sku_id = result
        print(f"SKU ID: {sku_id}")
        print(f"SKU序号: {sku_id_sequence}")
    else:
        print("✗ 获取订单信息失败")

if __name__ == "__main__":
    import os
    test_ticket_purchase()