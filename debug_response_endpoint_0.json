{"actionControl": {"calculatePriceControl": {"needCalc": "false", "calcFailSafe": "true", "calculateTag": "1_0_0_0"}, "renderingControl": {"renderingType": "1"}, "tradeControl": {"rtc": "true"}, "srcVerify": "0"}, "itemAdditionalInfo": {"performZoneNotice": "场次时间均为演出当地时间", "serviceFeeAgreementURL": "https://m.taopiaopiao.com/tickets/dianying/pages/alfheim/content.html?id=2204793&displayType=plain&hidetitle=yes&hsb=yes&interact=no&hideAuthorInfo=yes"}, "tradeInfo": {"itemTags4Trade": ["449602", "449538", "428610"], "anewUltron": "true", "inewUltron": "true", "hnewUltron": "true"}, "holidayCalendar": [{"dateStr": "20251005", "desc": "休", "isHoliday": "1"}, {"dateStr": "20251006", "desc": "中秋节", "isHoliday": "1"}, {"dateStr": "20251003", "desc": "休", "isHoliday": "1"}, {"dateStr": "20251004", "desc": "休", "isHoliday": "1"}, {"dateStr": "20251007", "desc": "休", "isHoliday": "1"}, {"dateStr": "20251008", "desc": "休", "isHoliday": "1"}, {"dateStr": "20251001", "desc": "国庆节", "isHoliday": "1"}, {"dateStr": "20251002", "desc": "休", "isHoliday": "1"}], "responseInfo": {"responseSuccess": "true", "responseCode": ""}, "skuRelatedText": {"registerToastMap": {"2": "若到货，我们将按照提交顺序为用户发送通知", "3": "开抢前将通过<b><font color=\"#4D4D56\">APP消息</font></b>推送提醒你来抢票～"}}}