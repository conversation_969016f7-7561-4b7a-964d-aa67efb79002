# -*- coding: UTF-8 -*-
"""
快速测试修复后的抢票脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("开始基本功能测试...")
        
        # 测试导入
        from Automatic_ticket_purchase import DaMaiTicket
        print("✓ 成功导入DaMaiTicket")
        
        # 创建实例
        ticket = DaMaiTicket()
        print("✓ 成功创建DaMaiTicket实例")
        
        # 测试商品ID
        print(f"商品ID: {ticket.item_id}")
        print(f"票价: {ticket.ticket_price}")
        print(f"观演人: {ticket.viewer}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_api_call():
    """测试API调用"""
    try:
        print("\n开始API调用测试...")
        
        import tools
        from Automatic_ticket_purchase import DaMaiTicket
        
        # 获取API参数
        commodity_param, ex_params = tools.get_api_param()
        print("✓ 成功获取API参数")
        
        # 创建抢票实例
        ticket = DaMaiTicket()
        
        # 测试获取订单信息（只测试一次，避免被限制）
        print("测试获取订单信息...")
        result = ticket.step1_get_order_info(ticket.item_id, commodity_param, ticket_price=ticket.ticket_price)
        
        if result and result[0] is not False:
            print("✓ API调用成功")
            return True
        else:
            print("✗ API调用失败，但这可能是正常的（需要进一步分析）")
            return False
            
    except Exception as e:
        print(f"✗ API调用测试失败: {e}")
        return False

def check_debug_files():
    """检查调试文件"""
    print("\n检查调试文件...")
    
    debug_files = [
        'debug_response_full.json',
        'debug_current_response.txt',
        'debug_parsed_response.json',
        'debug_page_data.json'
    ]
    
    for file in debug_files:
        if os.path.exists(file):
            print(f"✓ 找到调试文件: {file}")
            # 显示文件大小
            size = os.path.getsize(file)
            print(f"  文件大小: {size} 字节")
        else:
            print(f"✗ 未找到调试文件: {file}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("快速测试修复后的抢票脚本")
    print("=" * 50)
    
    # 基本功能测试
    basic_ok = test_basic_functionality()
    
    # API调用测试
    api_ok = test_api_call()
    
    # 检查调试文件
    check_debug_files()
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    if basic_ok:
        print("✓ 基本功能正常")
    else:
        print("✗ 基本功能有问题")
    
    if api_ok:
        print("✓ API调用正常")
    else:
        print("✗ API调用需要进一步检查")
    
    print("\n建议:")
    print("1. 检查生成的调试文件了解API响应结构")
    print("2. 确认商品ID是否有效")
    print("3. 如果API调用失败，可能需要更新API端点")
    
    if basic_ok:
        print("\n脚本基本结构正常，可以尝试运行完整脚本")
    else:
        print("\n需要先解决基本功能问题")

if __name__ == '__main__':
    main()
