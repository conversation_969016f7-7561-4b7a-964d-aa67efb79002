#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import sys
import json

print("Python版本:", sys.version)
print("开始测试...")

try:
    # 测试基本导入
    import requests
    print("✓ requests导入成功")
    
    import tools
    print("✓ tools导入成功")
    
    from Automatic_ticket_purchase import DaMaiTicket
    print("✓ DaMaiTicket导入成功")
    
    # 测试创建实例
    ticket = DaMaiTicket()
    print("✓ DaMaiTicket实例创建成功")
    
    # 测试读取调试文件
    try:
        with open('debug_response_full.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✓ 调试文件读取成功")
        print(f"调试文件包含字段: {list(data.keys())}")
        
        # 测试新的解析方法
        sku_info = ticket._extract_sku_info(data, 1399)
        if sku_info:
            print("✓ SKU信息提取成功")
        else:
            print("✗ SKU信息提取失败")
            
        status = ticket._get_ticket_status(data, 0)
        print(f"票务状态: {status}")
        
    except FileNotFoundError:
        print("✗ 调试文件不存在")
    except Exception as e:
        print(f"✗ 处理调试文件时出错: {e}")
    
except ImportError as e:
    print(f"✗ 导入错误: {e}")
except Exception as e:
    print(f"✗ 其他错误: {e}")

print("测试完成")
