# -*- coding: UTF-8 -*-
"""
测试修复后的抢票脚本
"""

import json
import time
import random
from Automatic_ticket_purchase import DaMaiTicket
import tools

def test_api_endpoints():
    """测试不同的API端点是否能正常工作"""
    print("=" * 50)
    print("测试API端点连通性")
    print("=" * 50)
    
    # 创建抢票实例
    ticket = DaMaiTicket()
    
    # 获取API参数
    try:
        commodity_param, ex_params = tools.get_api_param()
        print("✓ 成功获取API参数")
    except Exception as e:
        print(f"✗ 获取API参数失败: {e}")
        return False
    
    # 测试获取订单信息
    print("\n测试获取订单信息...")
    result = ticket.step1_get_order_info(ticket.item_id, commodity_param, ticket_price=ticket.ticket_price)
    
    if result and result[0] is not False:
        print("✓ 成功获取订单信息")
        ticket_info, sku_id_sequence, sku_id = result
        
        # 测试获取票务状态
        status = ticket._get_ticket_status(ticket_info, sku_id_sequence)
        if status:
            print(f"✓ 成功获取票务状态: {status}")
        else:
            print("✗ 无法获取票务状态")
        
        return True
    else:
        print("✗ 获取订单信息失败")
        return False

def test_response_parsing():
    """测试响应解析功能"""
    print("\n" + "=" * 50)
    print("测试响应解析功能")
    print("=" * 50)
    
    # 读取之前保存的调试响应
    try:
        with open('debug_response_full.json', 'r', encoding='utf-8') as f:
            test_response = json.load(f)
        
        ticket = DaMaiTicket()
        
        # 测试SKU信息提取
        sku_info = ticket._extract_sku_info(test_response, 1399)
        if sku_info:
            print("✓ 成功从调试响应中提取SKU信息")
        else:
            print("✗ 无法从调试响应中提取SKU信息")
        
        # 测试状态获取
        status = ticket._get_ticket_status(test_response, 0)
        if status:
            print(f"✓ 成功从调试响应中获取状态: {status}")
        else:
            print("✗ 无法从调试响应中获取状态")
            
    except FileNotFoundError:
        print("✗ 未找到调试响应文件，请先运行主脚本")
    except Exception as e:
        print(f"✗ 测试响应解析时发生错误: {e}")

def test_login_status():
    """测试登录状态"""
    print("\n" + "=" * 50)
    print("测试登录状态")
    print("=" * 50)
    
    try:
        # 检查是否有保存的cookies
        import os
        if os.path.exists('cookies.pkl'):
            cookies = tools.load_cookies()
            login_status = tools.check_login_status(cookies)
            if login_status:
                print("✓ 登录状态有效")
                return True
            else:
                print("✗ 登录状态无效，需要重新登录")
                return False
        else:
            print("✗ 未找到保存的登录信息")
            return False
    except Exception as e:
        print(f"✗ 检查登录状态时发生错误: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的抢票脚本...")
    
    # 测试登录状态
    login_ok = test_login_status()
    
    # 测试API端点
    api_ok = test_api_endpoints()
    
    # 测试响应解析
    test_response_parsing()
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    if login_ok and api_ok:
        print("✓ 所有关键功能测试通过，脚本应该可以正常工作")
        print("\n建议:")
        print("1. 确认商品ID是否正确且商品仍在售")
        print("2. 确认票价设置是否与实际票价匹配")
        print("3. 确认观演人信息是否正确")
    else:
        print("✗ 部分功能测试失败，需要进一步检查")
        if not login_ok:
            print("- 需要重新登录")
        if not api_ok:
            print("- API连接有问题，可能需要更新请求参数")

if __name__ == '__main__':
    main()
