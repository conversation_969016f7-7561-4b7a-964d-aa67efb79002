# 大麦网抢票脚本修复说明

## 问题诊断

根据您提供的错误信息，主要问题是：

```
错误: 响应中缺少'perform'字段
可用的字段: ['actionControl', 'itemAdditionalInfo', 'tradeInfo', 'holidayCalendar', 'responseInfo', 'skuRelatedText']
```

这表明大麦网的API结构发生了变化，原来的API端点不再返回票务信息。

## 修复内容

### 1. 主要修改文件：`Automatic_ticket_purchase.py`

#### 新增方法：
- `_extract_ticket_info_from_page()`: 从商品页面HTML中提取票务信息
- `_extract_sku_from_page_data()`: 从页面数据中提取SKU信息
- `_parse_response()`: 增强的响应解析方法
- `_extract_sku_info()`: 改进的SKU信息提取方法
- `_get_ticket_status()`: 增强的票务状态获取方法

#### 修改逻辑：
- 改进了API调用策略，优先尝试从商品页面获取信息
- 增加了多种响应格式的支持
- 增强了错误处理和调试信息输出

### 2. 新增调试工具

#### `debug_api.py`
- 专门用于调试API响应的工具
- 测试多个API端点
- 分析响应结构

#### `quick_test.py`
- 快速测试脚本基本功能
- 验证修复效果

## 使用方法

### 1. 检查和更新配置

在 `Automatic_ticket_purchase.py` 中确认以下信息：

```python
self.item_id: int = ************  # 商品id - 需要确认是否有效
self.ticket_price: int = 1399     # 购买指定票价 - 需要确认是否正确
self.viewer: list = ['张佳伟']     # 观影人 - 需要确认是否正确
```

### 2. 验证商品ID

访问以下链接确认商品是否存在：
```
https://detail.damai.cn/item.htm?id=************
```

如果页面显示商品不存在或已下架，需要更新商品ID。

### 3. 运行测试

```bash
# 快速测试基本功能
python quick_test.py

# 调试API响应
python debug_api.py

# 运行完整脚本
python Automatic_ticket_purchase.py --mode account
```

### 4. 检查调试文件

运行后会生成以下调试文件：
- `debug_current_response.txt`: 当前API响应
- `debug_parsed_response.json`: 解析后的JSON数据
- `debug_page_data.json`: 从页面提取的数据
- `debug_api_response_*.txt`: 各API端点的响应

## 可能遇到的问题

### 1. 商品ID无效
**症状**: 访问商品页面显示404或商品不存在
**解决**: 更新为有效的商品ID

### 2. 票价不匹配
**症状**: 找不到匹配的票价
**解决**: 检查实际票价并更新配置

### 3. API结构继续变化
**症状**: 仍然无法获取票务信息
**解决**: 
- 检查调试文件中的响应结构
- 根据实际结构更新解析逻辑
- 可能需要找到新的API端点

### 4. 反爬虫限制
**症状**: 请求被拦截或返回验证页面
**解决**:
- 增加请求间隔
- 使用代理IP
- 手动完成验证后再运行脚本

## 紧急解决方案

如果修复后仍无法正常工作，建议：

### 1. 手动分析API
1. 打开浏览器开发者工具
2. 访问商品页面
3. 查看网络请求，找到获取票务信息的真实API
4. 更新脚本中的API端点

### 2. 简化逻辑
暂时跳过复杂的状态判断，直接尝试购买：

```python
# 在主循环中简化逻辑
if ticket_info:
    # 直接尝试购买，不判断状态
    buy_serial_number = '{}_{}_{}'.format(self.item_id, self.buy_nums, 'default')
    submit_order_info = self.step2_click_buy_now(ex_params, buy_serial_number)
    if submit_order_info:
        break
```

### 3. 使用浏览器自动化
如果API方式完全失效，可以考虑使用Selenium等工具直接操作浏览器。

## 联系支持

如果问题仍然存在，建议：
1. 提供调试文件的内容
2. 确认商品ID和票价信息
3. 说明具体的错误信息

## 注意事项

1. **合规使用**: 请确保使用脚本符合大麦网的使用条款
2. **频率控制**: 避免过于频繁的请求，以免被限制
3. **及时更新**: 大麦网可能会继续更新API，需要定期检查和更新脚本
4. **备份数据**: 运行前备份重要的配置和数据
