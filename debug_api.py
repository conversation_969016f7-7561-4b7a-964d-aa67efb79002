# -*- coding: UTF-8 -*-
"""
调试大麦网API响应的脚本
"""

import requests
import json
import time
import re
from requests import session

def debug_api_response():
    """调试API响应"""
    
    # 商品ID
    item_id = 968367050836
    
    # 创建session
    sess = session()
    
    # 尝试不同的API端点
    endpoints = [
        {
            'name': '原始subpage API',
            'url': 'https://detail.damai.cn/subpage',
            'params': {
                'itemId': item_id,
                'bizCode': 'ali.china.damai',
                'scenario': 'itemsku',
                'exParams': '{"damai":"1","channel":"damai_app","umpChannel":"10002","atomSplit":"1","serviceVersion":"2.0.0"}',
                'dmChannel': 'damai@damaih5_h5',
                'osType': 'pc'
            }
        },
        {
            'name': '商品详情页面',
            'url': 'https://detail.damai.cn/item.htm',
            'params': {'id': item_id}
        },
        {
            'name': 'MTOP API',
            'url': 'https://mtop.damai.cn/h5/mtop.damai.item.getdetail/1.0/',
            'params': {
                'jsv': '2.7.2',
                'appKey': '12574478',
                't': str(int(time.time() * 1000)),
                'api': 'mtop.damai.item.getdetail',
                'v': '1.0',
                'type': 'jsonp',
                'dataType': 'jsonp',
                'callback': 'mtopjsonp1',
                'data': json.dumps({'itemId': str(item_id)})
            }
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Referer': f'https://detail.damai.cn/item.htm?id={item_id}'
    }
    
    for i, endpoint in enumerate(endpoints):
        print(f"\n{'='*60}")
        print(f"测试端点 {i+1}: {endpoint['name']}")
        print(f"URL: {endpoint['url']}")
        print(f"{'='*60}")
        
        try:
            response = sess.get(endpoint['url'], headers=headers, params=endpoint['params'], timeout=10)
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print(f"响应长度: {len(response.text)}")
            print(f"响应前500字符:")
            print(response.text[:500])
            print(f"响应后500字符:")
            print(response.text[-500:] if len(response.text) > 500 else "")
            
            # 保存完整响应
            filename = f'debug_api_response_{i+1}.txt'
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"端点: {endpoint['name']}\n")
                f.write(f"URL: {endpoint['url']}\n")
                f.write(f"参数: {endpoint['params']}\n")
                f.write(f"状态码: {response.status_code}\n")
                f.write(f"响应头: {dict(response.headers)}\n")
                f.write(f"响应内容:\n{response.text}")
            
            print(f"完整响应已保存到: {filename}")
            
            # 尝试解析JSON
            if endpoint['name'] == '商品详情页面':
                # 从HTML中提取JSON
                json_match = re.search(r'window\.__INIT_DATA__\s*=\s*({.*?});', response.text, re.DOTALL)
                if json_match:
                    try:
                        json_data = json.loads(json_match.group(1))
                        json_filename = f'debug_api_json_{i+1}.json'
                        with open(json_filename, 'w', encoding='utf-8') as f:
                            json.dump(json_data, f, ensure_ascii=False, indent=2)
                        print(f"提取的JSON已保存到: {json_filename}")
                        print(f"JSON顶级键: {list(json_data.keys())}")
                    except Exception as e:
                        print(f"JSON解析失败: {e}")
                else:
                    print("未找到JSON数据")
            else:
                # 尝试解析JSONP
                text = response.text.strip()
                json_str = None
                
                if text.startswith('null(') and text.endswith(')'):
                    json_str = text[5:-1]
                elif text.startswith('mtopjsonp1(') and text.endswith(')'):
                    json_str = text[11:-1]
                elif text.startswith('__jp0(') and text.endswith(')'):
                    json_str = text[6:-1]
                
                if json_str:
                    try:
                        json_data = json.loads(json_str)
                        json_filename = f'debug_api_json_{i+1}.json'
                        with open(json_filename, 'w', encoding='utf-8') as f:
                            json.dump(json_data, f, ensure_ascii=False, indent=2)
                        print(f"解析的JSON已保存到: {json_filename}")
                        print(f"JSON顶级键: {list(json_data.keys())}")
                    except Exception as e:
                        print(f"JSON解析失败: {e}")
                else:
                    print("无法识别JSONP格式")
            
        except Exception as e:
            print(f"请求失败: {e}")
        
        # 添加延时避免被限制
        time.sleep(2)

def analyze_existing_response():
    """分析现有的调试响应文件"""
    print(f"\n{'='*60}")
    print("分析现有调试响应")
    print(f"{'='*60}")
    
    try:
        with open('debug_response_full.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("现有响应结构分析:")
        print(f"顶级键: {list(data.keys())}")
        
        # 深度分析每个字段
        for key, value in data.items():
            print(f"\n{key}:")
            if isinstance(value, dict):
                print(f"  类型: 字典, 子键: {list(value.keys())}")
                # 检查是否包含SKU相关信息
                if any(sku_key in str(value).lower() for sku_key in ['sku', 'price', 'ticket', 'buy', 'btn']):
                    print(f"  可能包含票务信息!")
            elif isinstance(value, list):
                print(f"  类型: 列表, 长度: {len(value)}")
                if value and isinstance(value[0], dict):
                    print(f"  列表元素键: {list(value[0].keys())}")
            else:
                print(f"  类型: {type(value)}, 值: {str(value)[:100]}")
        
        # 搜索可能的票务信息
        print(f"\n搜索票务相关信息:")
        json_str = json.dumps(data, ensure_ascii=False)
        
        keywords = ['sku', 'price', 'ticket', 'buy', 'btn', 'status', 'sale']
        for keyword in keywords:
            if keyword in json_str.lower():
                print(f"  找到关键词: {keyword}")
        
    except FileNotFoundError:
        print("未找到现有调试响应文件")
    except Exception as e:
        print(f"分析现有响应时出错: {e}")

if __name__ == '__main__':
    print("开始调试大麦网API...")
    
    # 分析现有响应
    analyze_existing_response()
    
    # 测试新的API调用
    debug_api_response()
    
    print(f"\n{'='*60}")
    print("调试完成!")
    print("请检查生成的调试文件以了解API响应结构")
    print(f"{'='*60}")
